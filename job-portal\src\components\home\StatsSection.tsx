'use client';

import { useEffect, useState } from 'react';
import { 
  UserGroupIcon, 
  BuildingOfficeIcon, 
  DocumentTextIcon,
  CheckCircleIcon,
  GlobeAltIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

interface StatItem {
  id: string;
  icon: React.ComponentType<any>;
  value: number;
  label: string;
  suffix?: string;
  color: string;
}

const StatsSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [animatedValues, setAnimatedValues] = useState<Record<string, number>>({});

  const stats: StatItem[] = [
    {
      id: 'candidates',
      icon: UserGroupIcon,
      value: 15000,
      label: 'متقدم مسجل',
      suffix: '+',
      color: 'text-blue-600'
    },
    {
      id: 'companies',
      icon: BuildingOfficeIcon,
      value: 500,
      label: 'شركة شريكة',
      suffix: '+',
      color: 'text-green-600'
    },
    {
      id: 'jobs',
      icon: DocumentTextIcon,
      value: 2500,
      label: 'وظيفة متاحة',
      suffix: '+',
      color: 'text-purple-600'
    },
    {
      id: 'placements',
      icon: CheckCircleIcon,
      value: 8000,
      label: 'توظيف ناجح',
      suffix: '+',
      color: 'text-yellow-600'
    },
    {
      id: 'countries',
      icon: GlobeAltIcon,
      value: 25,
      label: 'دولة نعمل بها',
      suffix: '+',
      color: 'text-red-600'
    },
    {
      id: 'experience',
      icon: TrophyIcon,
      value: 10,
      label: 'سنوات خبرة',
      suffix: '+',
      color: 'text-indigo-600'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const element = document.getElementById('stats-section');
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  useEffect(() => {
    if (isVisible) {
      stats.forEach((stat) => {
        animateValue(stat.id, 0, stat.value, 2000);
      });
    }
  }, [isVisible]);

  const animateValue = (id: string, start: number, end: number, duration: number) => {
    const startTime = Date.now();
    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.floor(start + (end - start) * easeOutQuart);
      
      setAnimatedValues(prev => ({
        ...prev,
        [id]: currentValue
      }));

      if (progress >= 1) {
        clearInterval(timer);
      }
    }, 16);
  };

  return (
    <section id="stats-section" className="py-16 bg-gray-50" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            أرقام تتحدث عن نجاحنا
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            نفخر بالإنجازات التي حققناها في مجال التوظيف والربط بين المواهب وأصحاب العمل
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stats.map((stat) => {
            const IconComponent = stat.icon;
            const animatedValue = animatedValues[stat.id] || 0;
            
            return (
              <div
                key={stat.id}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className={`p-4 rounded-2xl bg-gray-50 ${stat.color}`}>
                    <IconComponent className="w-8 h-8" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-baseline space-x-1 rtl:space-x-reverse">
                      <span className={`text-3xl lg:text-4xl font-bold ${stat.color}`}>
                        {animatedValue.toLocaleString('ar-SA')}
                      </span>
                      {stat.suffix && (
                        <span className={`text-2xl font-bold ${stat.color}`}>
                          {stat.suffix}
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 font-medium mt-1">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-8 lg:p-12 text-white">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl lg:text-3xl font-bold mb-4">
                لماذا نحن الخيار الأفضل؟
              </h3>
              <ul className="space-y-3">
                <li className="flex items-center space-x-3 rtl:space-x-reverse">
                  <CheckCircleIcon className="w-6 h-6 text-green-400 flex-shrink-0" />
                  <span>خبرة أكثر من 10 سنوات في مجال التوظيف</span>
                </li>
                <li className="flex items-center space-x-3 rtl:space-x-reverse">
                  <CheckCircleIcon className="w-6 h-6 text-green-400 flex-shrink-0" />
                  <span>شبكة واسعة من الشركات الرائدة في المنطقة</span>
                </li>
                <li className="flex items-center space-x-3 rtl:space-x-reverse">
                  <CheckCircleIcon className="w-6 h-6 text-green-400 flex-shrink-0" />
                  <span>دعم شامل لإجراءات التأشيرة والسفر</span>
                </li>
                <li className="flex items-center space-x-3 rtl:space-x-reverse">
                  <CheckCircleIcon className="w-6 h-6 text-green-400 flex-shrink-0" />
                  <span>متابعة مستمرة حتى بعد التوظيف</span>
                </li>
              </ul>
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-2">98%</div>
                <div className="text-sm text-blue-100">معدل نجاح التوظيف</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-2">24/7</div>
                <div className="text-sm text-blue-100">دعم العملاء</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-2">30</div>
                <div className="text-sm text-blue-100">يوم متوسط التوظيف</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-2">100%</div>
                <div className="text-sm text-blue-100">خدمة مجانية للمتقدمين</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
