'use client';

import Link from 'next/link';
import { 
  CalendarDaysIcon,
  ClockIcon,
  ArrowRightIcon,
  TagIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishDate: string;
  readTime: string;
  category: string;
  tags: string[];
  image: string;
  featured: boolean;
}

const NewsSection = () => {
  const newsArticles: NewsArticle[] = [
    {
      id: '1',
      title: 'أحدث اتجاهات سوق العمل في دول الخليج لعام 2024',
      excerpt: 'تعرف على أهم التطورات والفرص الجديدة في سوق العمل الخليجي وأكثر التخصصات المطلوبة',
      content: 'يشهد سوق العمل في دول الخليج تطورات مهمة...',
      author: 'د. أحمد الخبير',
      publishDate: '2024-01-15',
      readTime: '5 دقائق',
      category: 'سوق العمل',
      tags: ['الخليج', 'وظائف', 'اتجاهات'],
      image: '/news/gulf-market-2024.jpg',
      featured: true
    },
    {
      id: '2',
      title: 'نصائح لكتابة سيرة ذاتية مميزة تجذب أصحاب العمل',
      excerpt: 'دليل شامل لإنشاء سيرة ذاتية احترافية تزيد من فرصك في الحصول على الوظيفة المناسبة',
      content: 'السيرة الذاتية هي بطاقة التعريف الأولى...',
      author: 'سارة المستشارة',
      publishDate: '2024-01-12',
      readTime: '7 دقائق',
      category: 'نصائح مهنية',
      tags: ['سيرة ذاتية', 'نصائح', 'توظيف'],
      image: '/news/cv-tips.jpg',
      featured: false
    },
    {
      id: '3',
      title: 'كيفية التحضير لمقابلة العمل عبر الإنترنت',
      excerpt: 'مع انتشار المقابلات الافتراضية، إليك أهم النصائح للنجاح في مقابلة العمل عن بُعد',
      content: 'أصبحت المقابلات عبر الإنترنت جزءاً أساسياً...',
      author: 'محمد الاستشاري',
      publishDate: '2024-01-10',
      readTime: '6 دقائق',
      category: 'مقابلات العمل',
      tags: ['مقابلات', 'عن بُعد', 'نصائح'],
      image: '/news/online-interview.jpg',
      featured: false
    },
    {
      id: '4',
      title: 'أفضل الشهادات المهنية المطلوبة في 2024',
      excerpt: 'قائمة بأهم الشهادات والدورات التدريبية التي تزيد من قيمتك في سوق العمل',
      content: 'في عالم متسارع التطور، تلعب الشهادات المهنية...',
      author: 'فاطمة الخبيرة',
      publishDate: '2024-01-08',
      readTime: '8 دقائق',
      category: 'التطوير المهني',
      tags: ['شهادات', 'تدريب', 'مهارات'],
      image: '/news/certifications-2024.jpg',
      featured: true
    }
  ];

  const categories = [
    { name: 'سوق العمل', count: 12, color: 'bg-blue-100 text-blue-600' },
    { name: 'نصائح مهنية', count: 8, color: 'bg-green-100 text-green-600' },
    { name: 'مقابلات العمل', count: 6, color: 'bg-purple-100 text-purple-600' },
    { name: 'التطوير المهني', count: 10, color: 'bg-orange-100 text-orange-600' }
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section className="py-20 bg-gray-50" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            أحدث الأخبار والمقالات
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ابق على اطلاع بأحدث التطورات في عالم التوظيف والمهن
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Featured Article */}
            <div className="mb-12">
              <div className="bg-white rounded-3xl overflow-hidden shadow-xl">
                <div className="relative h-64 lg:h-80 bg-gradient-to-r from-blue-600 to-purple-600">
                  <div className="absolute inset-0 bg-black bg-opacity-40"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
                      <span className="bg-yellow-500 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                        مقال مميز
                      </span>
                      <span className="bg-white bg-opacity-20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">
                        {newsArticles[0].category}
                      </span>
                    </div>
                    <h3 className="text-2xl lg:text-3xl font-bold mb-3 leading-tight">
                      {newsArticles[0].title}
                    </h3>
                    <p className="text-lg text-gray-200 mb-4 leading-relaxed">
                      {newsArticles[0].excerpt}
                    </p>
                    <div className="flex items-center space-x-6 rtl:space-x-reverse text-sm">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <UserIcon className="w-4 h-4" />
                        <span>{newsArticles[0].author}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <CalendarDaysIcon className="w-4 h-4" />
                        <span>{formatDate(newsArticles[0].publishDate)}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <ClockIcon className="w-4 h-4" />
                        <span>{newsArticles[0].readTime}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <Link
                    href={`/blog/${newsArticles[0].id}`}
                    className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors duration-200"
                  >
                    <span>اقرأ المقال كاملاً</span>
                    <ArrowRightIcon className="w-4 h-4 rtl:rotate-180" />
                  </Link>
                </div>
              </div>
            </div>

            {/* Other Articles */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {newsArticles.slice(1).map((article) => (
                <article
                  key={article.id}
                  className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
                >
                  <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300 relative">
                    <div className="absolute top-4 right-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                        article.category === 'نصائح مهنية' ? 'bg-green-100 text-green-600' :
                        article.category === 'مقابلات العمل' ? 'bg-purple-100 text-purple-600' :
                        'bg-orange-100 text-orange-600'
                      }`}>
                        {article.category}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-3 leading-tight">
                      {article.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {article.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <UserIcon className="w-4 h-4" />
                        <span>{article.author}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <ClockIcon className="w-4 h-4" />
                        <span>{article.readTime}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-500">
                        <CalendarDaysIcon className="w-4 h-4" />
                        <span>{formatDate(article.publishDate)}</span>
                      </div>
                      
                      <Link
                        href={`/blog/${article.id}`}
                        className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center space-x-1 rtl:space-x-reverse"
                      >
                        <span>اقرأ المزيد</span>
                        <ArrowRightIcon className="w-4 h-4 rtl:rotate-180" />
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Categories */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                التصنيفات
              </h3>
              <div className="space-y-3">
                {categories.map((category, index) => (
                  <Link
                    key={index}
                    href={`/blog/category/${category.name}`}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <span className="font-medium text-gray-700">
                      {category.name}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${category.color}`}>
                      {category.count}
                    </span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Newsletter Signup */}
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-6 text-white">
              <h3 className="text-xl font-bold mb-4">
                اشترك في النشرة الإخبارية
              </h3>
              <p className="text-blue-100 mb-6">
                احصل على أحدث المقالات والنصائح المهنية مباشرة في بريدك الإلكتروني
              </p>
              <div className="space-y-3">
                <input
                  type="email"
                  placeholder="بريدك الإلكتروني"
                  className="w-full px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                />
                <button className="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                  اشترك الآن
                </button>
              </div>
            </div>

            {/* Popular Tags */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                الكلمات المفتاحية الشائعة
              </h3>
              <div className="flex flex-wrap gap-2">
                {['وظائف', 'الخليج', 'سيرة ذاتية', 'مقابلات', 'تطوير مهني', 'نصائح', 'شهادات', 'تدريب'].map((tag, index) => (
                  <Link
                    key={index}
                    href={`/blog/tag/${tag}`}
                    className="inline-flex items-center space-x-1 rtl:space-x-reverse bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-full text-sm transition-colors duration-200"
                  >
                    <TagIcon className="w-3 h-3" />
                    <span>{tag}</span>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link
            href="/blog"
            className="inline-flex items-center space-x-3 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors duration-200"
          >
            <span>عرض جميع المقالات</span>
            <ArrowRightIcon className="w-5 h-5 rtl:rotate-180" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
