'use client';

import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';

interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  country: string;
  rating: number;
  content: string;
  avatar: string;
  jobTitle: string;
}

const TestimonialsSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const testimonials: Testimonial[] = [
    {
      id: '1',
      name: 'أحمد محمد العلي',
      position: 'مهندس برمجيات',
      company: 'شركة أرامكو السعودية',
      country: 'السعودية',
      rating: 5,
      content: 'بفضل بوابة الوظائف حصلت على وظيفة أحلامي في أرامكو. الفريق كان متعاوناً جداً وساعدني في كل خطوة من إجراءات التوظيف والانتقال.',
      avatar: '/avatars/ahmed.jpg',
      jobTitle: 'مهندس برمجيات أول'
    },
    {
      id: '2',
      name: 'فاطمة أحمد الزهراني',
      position: 'طبيبة أطفال',
      company: 'مستشفى الملك فيصل التخصصي',
      country: 'السعودية',
      rating: 5,
      content: 'خدمة ممتازة ومتابعة مستمرة. ساعدوني في الحصول على وظيفة في مستشفى مرموق مع راتب ممتاز ومزايا رائعة. أنصح الجميع بالتعامل معهم.',
      avatar: '/avatars/fatima.jpg',
      jobTitle: 'استشاري أطفال'
    },
    {
      id: '3',
      name: 'محمد عبدالله الكندي',
      position: 'مدير مشاريع',
      company: 'شركة نفط عمان',
      country: 'عمان',
      rating: 5,
      content: 'تجربة رائعة مع بوابة الوظائف. وجدت الوظيفة المناسبة في عمان وتم تسهيل جميع إجراءات السفر والإقامة. فريق محترف ومتميز.',
      avatar: '/avatars/mohammed.jpg',
      jobTitle: 'مدير مشاريع أول'
    },
    {
      id: '4',
      name: 'سارة خالد المنصوري',
      position: 'مديرة تسويق',
      company: 'مجموعة الإمارات',
      country: 'الإمارات',
      rating: 5,
      content: 'حصلت على وظيفة أحلامي في دبي بفضل الدعم المتميز من فريق بوابة الوظائف. المتابعة كانت ممتازة من البداية حتى النهاية.',
      avatar: '/avatars/sara.jpg',
      jobTitle: 'مديرة تسويق رقمي'
    },
    {
      id: '5',
      name: 'عبدالرحمن يوسف القطان',
      position: 'مهندس مدني',
      company: 'شركة قطر للبترول',
      country: 'قطر',
      rating: 5,
      content: 'خدمة احترافية من الدرجة الأولى. ساعدوني في العثور على وظيفة ممتازة في قطر مع راتب مجزي ومزايا رائعة. شكراً لكم.',
      avatar: '/avatars/abdulrahman.jpg',
      jobTitle: 'مهندس مشاريع'
    }
  ];

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    if (isAutoPlaying) {
      const interval = setInterval(nextTestimonial, 5000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, currentIndex]);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-5 h-5 ${
          index < rating ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section className="py-20 bg-white" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            ماذا يقول عملاؤنا
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            اقرأ تجارب العملاء الذين حققوا أحلامهم المهنية معنا
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div 
          className="relative"
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          <div className="overflow-hidden rounded-3xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(${currentIndex * -100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div
                  key={testimonial.id}
                  className="w-full flex-shrink-0 bg-gradient-to-br from-blue-50 to-indigo-100 p-8 lg:p-12"
                >
                  <div className="max-w-4xl mx-auto">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                      {/* Testimonial Content */}
                      <div className="lg:col-span-2 space-y-6">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          {renderStars(testimonial.rating)}
                        </div>
                        
                        <blockquote className="text-xl lg:text-2xl text-gray-800 leading-relaxed font-medium">
                          "{testimonial.content}"
                        </blockquote>
                        
                        <div className="space-y-2">
                          <div className="font-bold text-gray-900 text-lg">
                            {testimonial.name}
                          </div>
                          <div className="text-blue-600 font-medium">
                            {testimonial.jobTitle}
                          </div>
                          <div className="text-gray-600">
                            {testimonial.company} • {testimonial.country}
                          </div>
                        </div>
                      </div>

                      {/* Avatar and Company Info */}
                      <div className="text-center lg:text-right">
                        <div className="inline-block relative">
                          <div className="w-32 h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-2xl">
                            {testimonial.name.split(' ')[0][0]}{testimonial.name.split(' ')[1][0]}
                          </div>
                          <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                        
                        <div className="mt-6 p-4 bg-white rounded-xl shadow-lg">
                          <div className="text-sm text-gray-600 mb-1">نجح في الحصول على</div>
                          <div className="font-semibold text-gray-900">{testimonial.position}</div>
                          <div className="text-sm text-blue-600 mt-1">{testimonial.company}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 text-gray-600 p-3 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 text-gray-600 p-3 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl"
          >
            <ChevronRightIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center space-x-2 rtl:space-x-reverse mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-blue-600 w-8'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* Success Stats */}
        <div className="mt-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-3xl p-8 lg:p-12 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl lg:text-3xl font-bold mb-4">
              قصص نجاح حقيقية
            </h3>
            <p className="text-lg text-green-100">
              انضم إلى آلاف المحترفين الذين حققوا أحلامهم المهنية معنا
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-yellow-300 mb-2">98%</div>
              <div className="text-green-100">معدل رضا العملاء</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-yellow-300 mb-2">8,000+</div>
              <div className="text-green-100">توظيف ناجح</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-yellow-300 mb-2">30</div>
              <div className="text-green-100">يوم متوسط التوظيف</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-yellow-300 mb-2">24/7</div>
              <div className="text-green-100">دعم مستمر</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
