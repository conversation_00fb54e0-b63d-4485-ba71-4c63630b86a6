'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Bars3Icon, XMarkIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentLang, setCurrentLang] = useState('ar');
  const pathname = usePathname();

  const navigation = [
    { name: 'الرئيسية', href: '/', nameEn: 'Home' },
    { name: 'الوظائف', href: '/jobs', nameEn: 'Jobs' },
    { name: 'للمتقدمين', href: '/candidates', nameEn: 'Candidates' },
    { name: 'لأصحاب العمل', href: '/employers', nameEn: 'Employers' },
    { name: 'التأشيرات', href: '/visas', nameEn: 'Visas' },
    { name: 'من نحن', href: '/about', nameEn: 'About' },
    { name: 'اتصل بنا', href: '/contact', nameEn: 'Contact' },
  ];

  const toggleLanguage = () => {
    setCurrentLang(currentLang === 'ar' ? 'en' : 'ar');
    // Here you would implement actual language switching logic
  };

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50" dir={currentLang === 'ar' ? 'rtl' : 'ltr'}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">ج</span>
              </div>
              <div className="text-xl font-bold text-gray-900">
                {currentLang === 'ar' ? 'بوابة الوظائف' : 'Job Portal'}
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`text-sm font-medium transition-colors duration-200 ${
                  pathname === item.href
                    ? 'text-blue-600 border-b-2 border-blue-600 pb-1'
                    : 'text-gray-700 hover:text-blue-600'
                }`}
              >
                {currentLang === 'ar' ? item.name : item.nameEn}
              </Link>
            ))}
          </nav>

          {/* Language Toggle & Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4 rtl:space-x-reverse">
            <button
              onClick={toggleLanguage}
              className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 transition-colors"
            >
              <GlobeAltIcon className="w-5 h-5" />
              <span className="text-sm">{currentLang === 'ar' ? 'EN' : 'عربي'}</span>
            </button>
            
            <Link
              href="/login"
              className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
            >
              {currentLang === 'ar' ? 'تسجيل الدخول' : 'Login'}
            </Link>
            
            <Link
              href="/register"
              className="btn-primary text-sm"
            >
              {currentLang === 'ar' ? 'إنشاء حساب' : 'Register'}
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-blue-600 transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="flex flex-col space-y-4">
              {navigation.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`text-sm font-medium transition-colors duration-200 ${
                    pathname === item.href
                      ? 'text-blue-600'
                      : 'text-gray-700 hover:text-blue-600'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {currentLang === 'ar' ? item.name : item.nameEn}
                </Link>
              ))}
              
              <div className="flex flex-col space-y-2 pt-4 border-t border-gray-200">
                <button
                  onClick={toggleLanguage}
                  className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-blue-600 transition-colors text-sm"
                >
                  <GlobeAltIcon className="w-5 h-5" />
                  <span>{currentLang === 'ar' ? 'English' : 'عربي'}</span>
                </button>
                
                <Link
                  href="/login"
                  className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {currentLang === 'ar' ? 'تسجيل الدخول' : 'Login'}
                </Link>
                
                <Link
                  href="/register"
                  className="btn-primary text-sm inline-block text-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {currentLang === 'ar' ? 'إنشاء حساب' : 'Register'}
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
