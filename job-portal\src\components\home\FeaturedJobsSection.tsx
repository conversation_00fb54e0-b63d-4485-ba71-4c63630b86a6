'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  MapPinIcon,
  CurrencyDollarIcon,
  ClockIcon,
  BuildingOfficeIcon,
  ArrowRightIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  country: string;
  salary: string;
  type: string;
  experience: string;
  category: string;
  description: string;
  requirements: string[];
  benefits: string[];
  postedDate: string;
  urgent: boolean;
  featured: boolean;
}

const FeaturedJobsSection = () => {
  const [savedJobs, setSavedJobs] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState('all');

  const featuredJobs: Job[] = [
    {
      id: '1',
      title: 'مهندس برمجيات أول',
      company: 'شركة التقنية المتقدمة',
      location: 'الرياض',
      country: 'السعودية',
      salary: '15,000 - 20,000 ريال',
      type: 'دوام كامل',
      experience: '3-5 سنوات',
      category: 'تقنية المعلومات',
      description: 'نبحث عن مهندس برمجيات متمرس للانضمام إلى فريقنا التقني',
      requirements: ['خبرة في React و Node.js', 'إجادة اللغة الإنجليزية', 'شهادة جامعية في علوم الحاسوب'],
      benefits: ['تأمين صحي شامل', 'بدل سكن', 'تذاكر سفر سنوية'],
      postedDate: '2024-01-15',
      urgent: true,
      featured: true
    },
    {
      id: '2',
      title: 'طبيب أطفال',
      company: 'مستشفى الملك فيصل',
      location: 'دبي',
      country: 'الإمارات',
      salary: '25,000 - 35,000 درهم',
      type: 'دوام كامل',
      experience: '5+ سنوات',
      category: 'الطب والصحة',
      description: 'مطلوب طبيب أطفال مختص للعمل في مستشفى رائد',
      requirements: ['شهادة اختصاص في طب الأطفال', 'خبرة لا تقل عن 5 سنوات', 'إجادة العربية والإنجليزية'],
      benefits: ['راتب مجزي', 'سكن مفروش', 'تأمين صحي للعائلة'],
      postedDate: '2024-01-14',
      urgent: false,
      featured: true
    },
    {
      id: '3',
      title: 'مدير مبيعات',
      company: 'مجموعة الخليج التجارية',
      location: 'الدوحة',
      country: 'قطر',
      salary: '18,000 - 25,000 ريال قطري',
      type: 'دوام كامل',
      experience: '4-7 سنوات',
      category: 'المبيعات والتسويق',
      description: 'فرصة ممتازة لمدير مبيعات طموح في شركة رائدة',
      requirements: ['خبرة في إدارة فرق المبيعات', 'مهارات تواصل ممتازة', 'شهادة جامعية'],
      benefits: ['عمولات مجزية', 'سيارة شركة', 'بدل سكن'],
      postedDate: '2024-01-13',
      urgent: true,
      featured: true
    },
    {
      id: '4',
      title: 'مهندس مدني',
      company: 'شركة البناء الحديث',
      location: 'مسقط',
      country: 'عمان',
      salary: '1,200 - 1,800 ريال عماني',
      type: 'دوام كامل',
      experience: '2-4 سنوات',
      category: 'الهندسة',
      description: 'مطلوب مهندس مدني للعمل في مشاريع إنشائية كبرى',
      requirements: ['شهادة هندسة مدنية', 'خبرة في AutoCAD', 'إجادة اللغة الإنجليزية'],
      benefits: ['تأمين صحي', 'بدل مواصلات', 'إجازة سنوية مدفوعة'],
      postedDate: '2024-01-12',
      urgent: false,
      featured: true
    }
  ];

  const categories = [
    { id: 'all', name: 'جميع الوظائف', count: featuredJobs.length },
    { id: 'tech', name: 'تقنية المعلومات', count: 1 },
    { id: 'medical', name: 'الطب والصحة', count: 1 },
    { id: 'sales', name: 'المبيعات', count: 1 },
    { id: 'engineering', name: 'الهندسة', count: 1 }
  ];

  const toggleSaveJob = (jobId: string) => {
    setSavedJobs(prev => {
      const newSaved = new Set(prev);
      if (newSaved.has(jobId)) {
        newSaved.delete(jobId);
      } else {
        newSaved.add(jobId);
      }
      return newSaved;
    });
  };

  const filteredJobs = activeTab === 'all' 
    ? featuredJobs 
    : featuredJobs.filter(job => {
        switch (activeTab) {
          case 'tech': return job.category === 'تقنية المعلومات';
          case 'medical': return job.category === 'الطب والصحة';
          case 'sales': return job.category === 'المبيعات والتسويق';
          case 'engineering': return job.category === 'الهندسة';
          default: return true;
        }
      });

  return (
    <section className="py-20 bg-gray-50" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            الوظائف المميزة
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            اكتشف أحدث الفرص الوظيفية المتاحة في أفضل الشركات
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveTab(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                activeTab === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
              }`}
            >
              {category.name}
              <span className={`mr-2 text-sm ${
                activeTab === category.id ? 'text-blue-200' : 'text-gray-400'
              }`}>
                ({category.count})
              </span>
            </button>
          ))}
        </div>

        {/* Jobs Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {filteredJobs.map((job) => (
            <div
              key={job.id}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 relative"
            >
              {/* Job Header */}
              <div className="flex justify-between items-start mb-6">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                    {job.urgent && (
                      <span className="bg-red-100 text-red-600 text-xs font-semibold px-2 py-1 rounded-full">
                        عاجل
                      </span>
                    )}
                    {job.featured && (
                      <span className="bg-yellow-100 text-yellow-600 text-xs font-semibold px-2 py-1 rounded-full">
                        مميز
                      </span>
                    )}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {job.title}
                  </h3>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600 mb-2">
                    <BuildingOfficeIcon className="w-4 h-4" />
                    <span className="text-sm">{job.company}</span>
                  </div>
                </div>
                
                <button
                  onClick={() => toggleSaveJob(job.id)}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                >
                  {savedJobs.has(job.id) ? (
                    <BookmarkSolidIcon className="w-6 h-6 text-blue-600" />
                  ) : (
                    <BookmarkIcon className="w-6 h-6 text-gray-400" />
                  )}
                </button>
              </div>

              {/* Job Details */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
                  <MapPinIcon className="w-4 h-4" />
                  <span className="text-sm">{job.location}, {job.country}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
                  <CurrencyDollarIcon className="w-4 h-4" />
                  <span className="text-sm">{job.salary}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
                  <ClockIcon className="w-4 h-4" />
                  <span className="text-sm">{job.type}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-600">
                  <span className="text-sm">خبرة: {job.experience}</span>
                </div>
              </div>

              {/* Job Description */}
              <p className="text-gray-600 mb-6 leading-relaxed">
                {job.description}
              </p>

              {/* Requirements Preview */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-2">المتطلبات:</h4>
                <ul className="space-y-1">
                  {job.requirements.slice(0, 2).map((req, index) => (
                    <li key={index} className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                      <span>{req}</span>
                    </li>
                  ))}
                  {job.requirements.length > 2 && (
                    <li className="text-sm text-blue-600">
                      +{job.requirements.length - 2} متطلبات أخرى
                    </li>
                  )}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 rtl:space-x-reverse">
                <Link
                  href={`/jobs/${job.id}`}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 text-center"
                >
                  عرض التفاصيل
                </Link>
                <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                  تقدم الآن
                </button>
              </div>

              {/* Posted Date */}
              <div className="mt-4 text-xs text-gray-400 text-center">
                نُشر منذ {new Date(job.postedDate).toLocaleDateString('ar-SA')}
              </div>
            </div>
          ))}
        </div>

        {/* View All Jobs Button */}
        <div className="text-center">
          <Link
            href="/jobs"
            className="inline-flex items-center space-x-3 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors duration-200"
          >
            <span>عرض جميع الوظائف</span>
            <ArrowRightIcon className="w-5 h-5 rtl:rotate-180" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedJobsSection;
