{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Website/job-portal/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { \n  MagnifyingGlassIcon, \n  MapPinIcon, \n  BriefcaseIcon,\n  ChevronDownIcon \n} from '@heroicons/react/24/outline';\n\nconst HeroSection = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLocation, setSelectedLocation] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n\n  const locations = [\n    'السعودية',\n    'الإمارات',\n    'قطر',\n    'الكويت',\n    'البحرين',\n    'عمان',\n    'مصر',\n    'الأردن',\n    'لبنان'\n  ];\n\n  const categories = [\n    'تقنية المعلومات',\n    'الهندسة',\n    'الطب والصحة',\n    'التعليم',\n    'المالية والمحاسبة',\n    'التسويق والمبيعات',\n    'الإدارة',\n    'الضيافة والسياحة'\n  ];\n\n  const handleSearch = () => {\n    // Handle search logic here\n    console.log('Search:', { searchQuery, selectedLocation, selectedCategory });\n  };\n\n  return (\n    <section className=\"relative gradient-bg text-white overflow-hidden\" dir=\"rtl\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent\"></div>\n        <svg className=\"absolute bottom-0 left-0 w-full h-64\" viewBox=\"0 0 1200 120\" preserveAspectRatio=\"none\">\n          <path d=\"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\" opacity=\".25\" fill=\"currentColor\"></path>\n          <path d=\"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\" opacity=\".5\" fill=\"currentColor\"></path>\n          <path d=\"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z\" fill=\"currentColor\"></path>\n        </svg>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <div className=\"space-y-8\">\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl lg:text-6xl font-bold leading-tight\">\n                ابحث عن وظيفة\n                <span className=\"block text-yellow-400\">أحلامك</span>\n                في الخليج\n              </h1>\n              <p className=\"text-xl text-blue-100 leading-relaxed\">\n                نربط بين المواهب العربية وأفضل الفرص الوظيفية في دول الخليج وحول العالم. \n                ابدأ رحلتك المهنية معنا اليوم.\n              </p>\n            </div>\n\n            {/* Search Form */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-2xl\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                {/* Job Search */}\n                <div className=\"relative\">\n                  <MagnifyingGlassIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"ابحث عن وظيفة...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900\"\n                  />\n                </div>\n\n                {/* Location */}\n                <div className=\"relative\">\n                  <MapPinIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <select\n                    value={selectedLocation}\n                    onChange={(e) => setSelectedLocation(e.target.value)}\n                    className=\"w-full pr-10 pl-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 appearance-none\"\n                  >\n                    <option value=\"\">اختر الموقع</option>\n                    {locations.map((location) => (\n                      <option key={location} value={location}>\n                        {location}\n                      </option>\n                    ))}\n                  </select>\n                  <ChevronDownIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none\" />\n                </div>\n\n                {/* Category */}\n                <div className=\"relative\">\n                  <BriefcaseIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"w-full pr-10 pl-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 appearance-none\"\n                  >\n                    <option value=\"\">اختر التخصص</option>\n                    {categories.map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                  <ChevronDownIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none\" />\n                </div>\n              </div>\n\n              <button\n                onClick={handleSearch}\n                className=\"w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 rtl:space-x-reverse\"\n              >\n                <MagnifyingGlassIcon className=\"w-5 h-5\" />\n                <span>ابحث الآن</span>\n              </button>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"flex flex-wrap gap-4\">\n              <Link\n                href=\"/jobs\"\n                className=\"bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-200 border border-white/20\"\n              >\n                تصفح جميع الوظائف\n              </Link>\n              <Link\n                href=\"/candidates/register\"\n                className=\"bg-yellow-500 hover:bg-yellow-600 text-gray-900 px-6 py-3 rounded-lg transition-colors duration-200 font-semibold\"\n              >\n                سجل كمتقدم\n              </Link>\n              <Link\n                href=\"/employers/register\"\n                className=\"bg-transparent hover:bg-white/10 text-white px-6 py-3 rounded-lg transition-all duration-200 border border-white/30\"\n              >\n                سجل كصاحب عمل\n              </Link>\n            </div>\n          </div>\n\n          {/* Hero Image */}\n          <div className=\"relative\">\n            <div className=\"relative z-10\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20\">\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n                    <div className=\"w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center\">\n                      <BriefcaseIcon className=\"w-8 h-8 text-gray-900\" />\n                    </div>\n                    <div>\n                      <h3 className=\"text-xl font-semibold\">أكثر من 2,500 وظيفة</h3>\n                      <p className=\"text-blue-100\">متاحة الآن في جميع التخصصات</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"bg-white/5 rounded-xl p-4 text-center\">\n                      <div className=\"text-2xl font-bold text-yellow-400\">15K+</div>\n                      <div className=\"text-sm text-blue-100\">متقدم مسجل</div>\n                    </div>\n                    <div className=\"bg-white/5 rounded-xl p-4 text-center\">\n                      <div className=\"text-2xl font-bold text-yellow-400\">500+</div>\n                      <div className=\"text-sm text-blue-100\">شركة شريكة</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Floating Elements */}\n            <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-yellow-400/20 rounded-full blur-xl\"></div>\n            <div className=\"absolute -bottom-8 -left-8 w-32 h-32 bg-blue-400/20 rounded-full blur-xl\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAWA,MAAM,cAAc;;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,WAAW;YAAE;YAAa;YAAkB;QAAiB;IAC3E;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAkD,KAAI;;0BAEvE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAuC,SAAQ;wBAAe,qBAAoB;;0CAC/F,6LAAC;gCAAK,GAAE;gCAAwN,SAAQ;gCAAM,MAAK;;;;;;0CACnP,6LAAC;gCAAK,GAAE;gCAA+W,SAAQ;gCAAK,MAAK;;;;;;0CACzY,6LAAC;gCAAK,GAAE;gCAAsL,MAAK;;;;;;;;;;;;;;;;;;0BAIvM,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAA+C;8DAE3D,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAa;;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAOvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,wOAAA,CAAA,sBAAmB;4DAAC,WAAU;;;;;;sEAC/B,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,WAAU;;;;;;;;;;;;8DAKd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4DACnD,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;wEAAsB,OAAO;kFAC3B;uEADU;;;;;;;;;;;sEAKjB,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;;8DAI7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,4NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4DACnD,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wEAAsB,OAAO;kFAC3B;uEADU;;;;;;;;;;;sEAKjB,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAI/B,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,wOAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,4NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;sEAE3B,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAIjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQjD,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAtLM;KAAA;uCAwLS", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Website/job-portal/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { \n  UserGroupIcon, \n  BuildingOfficeIcon, \n  DocumentTextIcon,\n  CheckCircleIcon,\n  GlobeAltIcon,\n  TrophyIcon\n} from '@heroicons/react/24/outline';\n\ninterface StatItem {\n  id: string;\n  icon: React.ComponentType<any>;\n  value: number;\n  label: string;\n  suffix?: string;\n  color: string;\n}\n\nconst StatsSection = () => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [animatedValues, setAnimatedValues] = useState<Record<string, number>>({});\n\n  const stats: StatItem[] = [\n    {\n      id: 'candidates',\n      icon: UserGroupIcon,\n      value: 15000,\n      label: 'متقدم مسجل',\n      suffix: '+',\n      color: 'text-blue-600'\n    },\n    {\n      id: 'companies',\n      icon: BuildingOfficeIcon,\n      value: 500,\n      label: 'شركة شريكة',\n      suffix: '+',\n      color: 'text-green-600'\n    },\n    {\n      id: 'jobs',\n      icon: DocumentTextIcon,\n      value: 2500,\n      label: 'وظيفة متاحة',\n      suffix: '+',\n      color: 'text-purple-600'\n    },\n    {\n      id: 'placements',\n      icon: CheckCircleIcon,\n      value: 8000,\n      label: 'توظيف ناجح',\n      suffix: '+',\n      color: 'text-yellow-600'\n    },\n    {\n      id: 'countries',\n      icon: GlobeAltIcon,\n      value: 25,\n      label: 'دولة نعمل بها',\n      suffix: '+',\n      color: 'text-red-600'\n    },\n    {\n      id: 'experience',\n      icon: TrophyIcon,\n      value: 10,\n      label: 'سنوات خبرة',\n      suffix: '+',\n      color: 'text-indigo-600'\n    }\n  ];\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.3 }\n    );\n\n    const element = document.getElementById('stats-section');\n    if (element) {\n      observer.observe(element);\n    }\n\n    return () => {\n      if (element) {\n        observer.unobserve(element);\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    if (isVisible) {\n      stats.forEach((stat) => {\n        animateValue(stat.id, 0, stat.value, 2000);\n      });\n    }\n  }, [isVisible]);\n\n  const animateValue = (id: string, start: number, end: number, duration: number) => {\n    const startTime = Date.now();\n    const timer = setInterval(() => {\n      const elapsed = Date.now() - startTime;\n      const progress = Math.min(elapsed / duration, 1);\n      \n      // Easing function for smooth animation\n      const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n      const currentValue = Math.floor(start + (end - start) * easeOutQuart);\n      \n      setAnimatedValues(prev => ({\n        ...prev,\n        [id]: currentValue\n      }));\n\n      if (progress >= 1) {\n        clearInterval(timer);\n      }\n    }, 16);\n  };\n\n  return (\n    <section id=\"stats-section\" className=\"py-16 bg-gray-50\" dir=\"rtl\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            أرقام تتحدث عن نجاحنا\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            نفخر بالإنجازات التي حققناها في مجال التوظيف والربط بين المواهب وأصحاب العمل\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {stats.map((stat) => {\n            const IconComponent = stat.icon;\n            const animatedValue = animatedValues[stat.id] || 0;\n            \n            return (\n              <div\n                key={stat.id}\n                className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100\"\n              >\n                <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n                  <div className={`p-4 rounded-2xl bg-gray-50 ${stat.color}`}>\n                    <IconComponent className=\"w-8 h-8\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-baseline space-x-1 rtl:space-x-reverse\">\n                      <span className={`text-3xl lg:text-4xl font-bold ${stat.color}`}>\n                        {animatedValue.toLocaleString('ar-SA')}\n                      </span>\n                      {stat.suffix && (\n                        <span className={`text-2xl font-bold ${stat.color}`}>\n                          {stat.suffix}\n                        </span>\n                      )}\n                    </div>\n                    <p className=\"text-gray-600 font-medium mt-1\">\n                      {stat.label}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"mt-16 bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-8 lg:p-12 text-white\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h3 className=\"text-2xl lg:text-3xl font-bold mb-4\">\n                لماذا نحن الخيار الأفضل؟\n              </h3>\n              <ul className=\"space-y-3\">\n                <li className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <CheckCircleIcon className=\"w-6 h-6 text-green-400 flex-shrink-0\" />\n                  <span>خبرة أكثر من 10 سنوات في مجال التوظيف</span>\n                </li>\n                <li className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <CheckCircleIcon className=\"w-6 h-6 text-green-400 flex-shrink-0\" />\n                  <span>شبكة واسعة من الشركات الرائدة في المنطقة</span>\n                </li>\n                <li className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <CheckCircleIcon className=\"w-6 h-6 text-green-400 flex-shrink-0\" />\n                  <span>دعم شامل لإجراءات التأشيرة والسفر</span>\n                </li>\n                <li className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <CheckCircleIcon className=\"w-6 h-6 text-green-400 flex-shrink-0\" />\n                  <span>متابعة مستمرة حتى بعد التوظيف</span>\n                </li>\n              </ul>\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-6\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center\">\n                <div className=\"text-3xl font-bold text-yellow-400 mb-2\">98%</div>\n                <div className=\"text-sm text-blue-100\">معدل نجاح التوظيف</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center\">\n                <div className=\"text-3xl font-bold text-yellow-400 mb-2\">24/7</div>\n                <div className=\"text-sm text-blue-100\">دعم العملاء</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center\">\n                <div className=\"text-3xl font-bold text-yellow-400 mb-2\">30</div>\n                <div className=\"text-sm text-blue-100\">يوم متوسط التوظيف</div>\n              </div>\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center\">\n                <div className=\"text-3xl font-bold text-yellow-400 mb-2\">100%</div>\n                <div className=\"text-sm text-blue-100\">خدمة مجانية للمتقدمين</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default StatsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAqBA,MAAM,eAAe;;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9E,MAAM,QAAoB;QACxB;YACE,IAAI;YACJ,MAAM,4NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,sOAAA,CAAA,qBAAkB;YACxB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,kOAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,gOAAA,CAAA,kBAAe;YACrB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,0NAAA,CAAA,eAAY;YAClB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,sNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QACT;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,IAAI;0CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;yCACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,SAAS;gBACX,SAAS,OAAO,CAAC;YACnB;YAEA;0CAAO;oBACL,IAAI,SAAS;wBACX,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW;gBACb,MAAM,OAAO;8CAAC,CAAC;wBACb,aAAa,KAAK,EAAE,EAAE,GAAG,KAAK,KAAK,EAAE;oBACvC;;YACF;QACF;iCAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAC,IAAY,OAAe,KAAa;QAC5D,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,QAAQ,YAAY;YACxB,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU;YAE9C,uCAAuC;YACvC,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;YAChD,MAAM,eAAe,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI;YAExD,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,CAAC,GAAG,EAAE;gBACR,CAAC;YAED,IAAI,YAAY,GAAG;gBACjB,cAAc;YAChB;QACF,GAAG;IACL;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAgB,WAAU;QAAmB,KAAI;kBAC3D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC;wBACV,MAAM,gBAAgB,KAAK,IAAI;wBAC/B,MAAM,gBAAgB,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI;wBAEjD,qBACE,6LAAC;4BAEC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,EAAE;kDACxD,cAAA,6LAAC;4CAAc,WAAU;;;;;;;;;;;kDAE3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAC,+BAA+B,EAAE,KAAK,KAAK,EAAE;kEAC5D,cAAc,cAAc,CAAC;;;;;;oDAE/B,KAAK,MAAM,kBACV,6LAAC;wDAAK,WAAW,CAAC,mBAAmB,EAAE,KAAK,KAAK,EAAE;kEAChD,KAAK,MAAM;;;;;;;;;;;;0DAIlB,6LAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;2BAnBZ,KAAK,EAAE;;;;;oBAyBlB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDAGpD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;kEAC3B,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;kEAC3B,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;kEAC3B,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;kEAC3B,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GA7MM;KAAA;uCA+MS", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Website/job-portal/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { \n  GlobeAltIcon,\n  DocumentCheckIcon,\n  UserGroupIcon,\n  BuildingOfficeIcon,\n  AcademicCapIcon,\n  HeartIcon,\n  CogIcon,\n  TruckIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline';\n\ninterface Service {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  color: string;\n  bgColor: string;\n  features: string[];\n  link: string;\n}\n\nconst ServicesSection = () => {\n  const services: Service[] = [\n    {\n      id: 'international',\n      title: 'التوظيف الدولي',\n      description: 'نربطك بأفضل الفرص الوظيفية في دول الخليج وحول العالم',\n      icon: GlobeAltIcon,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      features: [\n        'وظائف في دول الخليج',\n        'فرص في أوروبا وأمريكا',\n        'وظائف في آسيا وأفريقيا',\n        'رواتب تنافسية'\n      ],\n      link: '/services/international'\n    },\n    {\n      id: 'visa',\n      title: 'خدمات التأشيرة',\n      description: 'نساعدك في جميع إجراءات التأشيرة والسفر بسهولة ويسر',\n      icon: DocumentCheckIcon,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      features: [\n        'استخراج تأشيرة العمل',\n        'ترجمة الوثائق',\n        'الفحص الطبي',\n        'متابعة الإجراءات'\n      ],\n      link: '/services/visa'\n    },\n    {\n      id: 'recruitment',\n      title: 'استشارات التوظيف',\n      description: 'خدمات استشارية متخصصة للشركات في مجال التوظيف',\n      icon: UserGroupIcon,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      features: [\n        'تقييم المرشحين',\n        'اختبارات المهارات',\n        'المقابلات المتخصصة',\n        'التوظيف السريع'\n      ],\n      link: '/services/recruitment'\n    },\n    {\n      id: 'corporate',\n      title: 'الحلول المؤسسية',\n      description: 'حلول توظيف شاملة للشركات الكبيرة والمؤسسات',\n      icon: BuildingOfficeIcon,\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-50',\n      features: [\n        'توظيف جماعي',\n        'إدارة المواهب',\n        'التدريب والتطوير',\n        'الاستشارات الإدارية'\n      ],\n      link: '/services/corporate'\n    }\n  ];\n\n  const specializations = [\n    {\n      icon: CogIcon,\n      title: 'الهندسة والتقنية',\n      count: '850+ وظيفة',\n      color: 'text-blue-600'\n    },\n    {\n      icon: HeartIcon,\n      title: 'الطب والصحة',\n      count: '420+ وظيفة',\n      color: 'text-red-600'\n    },\n    {\n      icon: AcademicCapIcon,\n      title: 'التعليم والتدريب',\n      count: '320+ وظيفة',\n      color: 'text-green-600'\n    },\n    {\n      icon: TruckIcon,\n      title: 'اللوجستيات والنقل',\n      count: '280+ وظيفة',\n      color: 'text-yellow-600'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\" dir=\"rtl\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            خدماتنا المتميزة\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            نقدم مجموعة شاملة من الخدمات لضمان حصولك على أفضل الفرص الوظيفية\n          </p>\n        </div>\n\n        {/* Main Services */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20\">\n          {services.map((service) => {\n            const IconComponent = service.icon;\n            \n            return (\n              <div\n                key={service.id}\n                className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100\"\n              >\n                <div className={`w-16 h-16 ${service.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                  <IconComponent className={`w-8 h-8 ${service.color}`} />\n                </div>\n                \n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                  {service.title}\n                </h3>\n                \n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n                \n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600\">\n                      <div className={`w-2 h-2 rounded-full ${service.color.replace('text-', 'bg-')}`}></div>\n                      <span>{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n                \n                <Link\n                  href={service.link}\n                  className={`inline-flex items-center space-x-2 rtl:space-x-reverse ${service.color} hover:underline font-medium group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-200`}\n                >\n                  <span>اعرف المزيد</span>\n                  <ArrowRightIcon className=\"w-4 h-4 rtl:rotate-180\" />\n                </Link>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Specializations */}\n        <div className=\"bg-gray-50 rounded-3xl p-8 lg:p-12\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-4\">\n              التخصصات الأكثر طلباً\n            </h3>\n            <p className=\"text-lg text-gray-600\">\n              اكتشف الفرص المتاحة في أهم التخصصات المطلوبة في السوق\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {specializations.map((spec, index) => {\n              const IconComponent = spec.icon;\n              \n              return (\n                <div\n                  key={index}\n                  className=\"bg-white rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300\"\n                >\n                  <div className=\"flex justify-center mb-4\">\n                    <div className={`w-12 h-12 bg-gray-50 rounded-xl flex items-center justify-center`}>\n                      <IconComponent className={`w-6 h-6 ${spec.color}`} />\n                    </div>\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    {spec.title}\n                  </h4>\n                  <p className={`text-sm font-medium ${spec.color}`}>\n                    {spec.count}\n                  </p>\n                </div>\n              );\n            })}\n          </div>\n\n          <div className=\"text-center\">\n            <Link\n              href=\"/jobs\"\n              className=\"inline-flex items-center space-x-3 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors duration-200\"\n            >\n              <span>تصفح جميع الوظائف</span>\n              <ArrowRightIcon className=\"w-5 h-5 rtl:rotate-180\" />\n            </Link>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 lg:p-12 text-white text-center\">\n          <h3 className=\"text-2xl lg:text-3xl font-bold mb-4\">\n            هل تحتاج مساعدة في العثور على الوظيفة المناسبة؟\n          </h3>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            فريقنا من الخبراء جاهز لمساعدتك في كل خطوة من رحلة البحث عن العمل\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-xl transition-colors duration-200\"\n            >\n              تواصل معنا\n            </Link>\n            <Link\n              href=\"/candidates/register\"\n              className=\"bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 rounded-xl transition-all duration-200\"\n            >\n              سجل الآن مجاناً\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA0BA,MAAM,kBAAkB;IACtB,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0NAAA,CAAA,eAAY;YAClB,OAAO;YACP,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,oOAAA,CAAA,oBAAiB;YACvB,OAAO;YACP,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,4NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sOAAA,CAAA,qBAAkB;YACxB,OAAO;YACP,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,MAAM;QACR;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,MAAM,gNAAA,CAAA,UAAO;YACb,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,oNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,gOAAA,CAAA,kBAAe;YACrB,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,oNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAAiB,KAAI;kBACtC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,gBAAgB,QAAQ,IAAI;wBAElC,qBACE,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,OAAO,CAAC,0GAA0G,CAAC;8CACtJ,cAAA,6LAAC;wCAAc,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;8CAGtD,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAGtB,6LAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;4CAAe,WAAU;;8DACxB,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,QAAQ,KAAK,CAAC,OAAO,CAAC,SAAS,QAAQ;;;;;;8DAC/E,6LAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;8CAOb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,QAAQ,IAAI;oCAClB,WAAW,CAAC,uDAAuD,EAAE,QAAQ,KAAK,CAAC,uHAAuH,CAAC;;sDAE3M,6LAAC;sDAAK;;;;;;sDACN,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;2BA7BvB,QAAQ,EAAE;;;;;oBAiCrB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM;gCAC1B,MAAM,gBAAgB,KAAK,IAAI;gCAE/B,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAW,CAAC,gEAAgE,CAAC;0DAChF,cAAA,6LAAC;oDAAc,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;sDAGrD,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAE,WAAW,CAAC,oBAAoB,EAAE,KAAK,KAAK,EAAE;sDAC9C,KAAK,KAAK;;;;;;;mCAZR;;;;;4BAgBX;;;;;;sCAGF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;kDAAK;;;;;;kDACN,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA5NM;uCA8NS", "debugId": null}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Website/job-portal/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { \n  MapPinIcon,\n  CurrencyDollarIcon,\n  ClockIcon,\n  BuildingOfficeIcon,\n  ArrowRightIcon,\n  BookmarkIcon\n} from '@heroicons/react/24/outline';\nimport { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';\n\ninterface Job {\n  id: string;\n  title: string;\n  company: string;\n  location: string;\n  country: string;\n  salary: string;\n  type: string;\n  experience: string;\n  category: string;\n  description: string;\n  requirements: string[];\n  benefits: string[];\n  postedDate: string;\n  urgent: boolean;\n  featured: boolean;\n}\n\nconst FeaturedJobsSection = () => {\n  const [savedJobs, setSavedJobs] = useState<Set<string>>(new Set());\n  const [activeTab, setActiveTab] = useState('all');\n\n  const featuredJobs: Job[] = [\n    {\n      id: '1',\n      title: 'مهندس برمجيات أول',\n      company: 'شركة التقنية المتقدمة',\n      location: 'الرياض',\n      country: 'السعودية',\n      salary: '15,000 - 20,000 ريال',\n      type: 'دوام كامل',\n      experience: '3-5 سنوات',\n      category: 'تقنية المعلومات',\n      description: 'نبحث عن مهندس برمجيات متمرس للانضمام إلى فريقنا التقني',\n      requirements: ['خبرة في React و Node.js', 'إجادة اللغة الإنجليزية', 'شهادة جامعية في علوم الحاسوب'],\n      benefits: ['تأمين صحي شامل', 'بدل سكن', 'تذاكر سفر سنوية'],\n      postedDate: '2024-01-15',\n      urgent: true,\n      featured: true\n    },\n    {\n      id: '2',\n      title: 'طبيب أطفال',\n      company: 'مستشفى الملك فيصل',\n      location: 'دبي',\n      country: 'الإمارات',\n      salary: '25,000 - 35,000 درهم',\n      type: 'دوام كامل',\n      experience: '5+ سنوات',\n      category: 'الطب والصحة',\n      description: 'مطلوب طبيب أطفال مختص للعمل في مستشفى رائد',\n      requirements: ['شهادة اختصاص في طب الأطفال', 'خبرة لا تقل عن 5 سنوات', 'إجادة العربية والإنجليزية'],\n      benefits: ['راتب مجزي', 'سكن مفروش', 'تأمين صحي للعائلة'],\n      postedDate: '2024-01-14',\n      urgent: false,\n      featured: true\n    },\n    {\n      id: '3',\n      title: 'مدير مبيعات',\n      company: 'مجموعة الخليج التجارية',\n      location: 'الدوحة',\n      country: 'قطر',\n      salary: '18,000 - 25,000 ريال قطري',\n      type: 'دوام كامل',\n      experience: '4-7 سنوات',\n      category: 'المبيعات والتسويق',\n      description: 'فرصة ممتازة لمدير مبيعات طموح في شركة رائدة',\n      requirements: ['خبرة في إدارة فرق المبيعات', 'مهارات تواصل ممتازة', 'شهادة جامعية'],\n      benefits: ['عمولات مجزية', 'سيارة شركة', 'بدل سكن'],\n      postedDate: '2024-01-13',\n      urgent: true,\n      featured: true\n    },\n    {\n      id: '4',\n      title: 'مهندس مدني',\n      company: 'شركة البناء الحديث',\n      location: 'مسقط',\n      country: 'عمان',\n      salary: '1,200 - 1,800 ريال عماني',\n      type: 'دوام كامل',\n      experience: '2-4 سنوات',\n      category: 'الهندسة',\n      description: 'مطلوب مهندس مدني للعمل في مشاريع إنشائية كبرى',\n      requirements: ['شهادة هندسة مدنية', 'خبرة في AutoCAD', 'إجادة اللغة الإنجليزية'],\n      benefits: ['تأمين صحي', 'بدل مواصلات', 'إجازة سنوية مدفوعة'],\n      postedDate: '2024-01-12',\n      urgent: false,\n      featured: true\n    }\n  ];\n\n  const categories = [\n    { id: 'all', name: 'جميع الوظائف', count: featuredJobs.length },\n    { id: 'tech', name: 'تقنية المعلومات', count: 1 },\n    { id: 'medical', name: 'الطب والصحة', count: 1 },\n    { id: 'sales', name: 'المبيعات', count: 1 },\n    { id: 'engineering', name: 'الهندسة', count: 1 }\n  ];\n\n  const toggleSaveJob = (jobId: string) => {\n    setSavedJobs(prev => {\n      const newSaved = new Set(prev);\n      if (newSaved.has(jobId)) {\n        newSaved.delete(jobId);\n      } else {\n        newSaved.add(jobId);\n      }\n      return newSaved;\n    });\n  };\n\n  const filteredJobs = activeTab === 'all' \n    ? featuredJobs \n    : featuredJobs.filter(job => {\n        switch (activeTab) {\n          case 'tech': return job.category === 'تقنية المعلومات';\n          case 'medical': return job.category === 'الطب والصحة';\n          case 'sales': return job.category === 'المبيعات والتسويق';\n          case 'engineering': return job.category === 'الهندسة';\n          default: return true;\n        }\n      });\n\n  return (\n    <section className=\"py-20 bg-gray-50\" dir=\"rtl\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            الوظائف المميزة\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            اكتشف أحدث الفرص الوظيفية المتاحة في أفضل الشركات\n          </p>\n        </div>\n\n        {/* Category Tabs */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              onClick={() => setActiveTab(category.id)}\n              className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${\n                activeTab === category.id\n                  ? 'bg-blue-600 text-white shadow-lg'\n                  : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'\n              }`}\n            >\n              {category.name}\n              <span className={`mr-2 text-sm ${\n                activeTab === category.id ? 'text-blue-200' : 'text-gray-400'\n              }`}>\n                ({category.count})\n              </span>\n            </button>\n          ))}\n        </div>\n\n        {/* Jobs Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\">\n          {filteredJobs.map((job) => (\n            <div\n              key={job.id}\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 relative\"\n            >\n              {/* Job Header */}\n              <div className=\"flex justify-between items-start mb-6\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2 rtl:space-x-reverse mb-2\">\n                    {job.urgent && (\n                      <span className=\"bg-red-100 text-red-600 text-xs font-semibold px-2 py-1 rounded-full\">\n                        عاجل\n                      </span>\n                    )}\n                    {job.featured && (\n                      <span className=\"bg-yellow-100 text-yellow-600 text-xs font-semibold px-2 py-1 rounded-full\">\n                        مميز\n                      </span>\n                    )}\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n                    {job.title}\n                  </h3>\n                  <div className=\"flex items-center space-x-2 rtl:space-x-reverse text-gray-600 mb-2\">\n                    <BuildingOfficeIcon className=\"w-4 h-4\" />\n                    <span className=\"text-sm\">{job.company}</span>\n                  </div>\n                </div>\n                \n                <button\n                  onClick={() => toggleSaveJob(job.id)}\n                  className=\"p-2 rounded-full hover:bg-gray-100 transition-colors\"\n                >\n                  {savedJobs.has(job.id) ? (\n                    <BookmarkSolidIcon className=\"w-6 h-6 text-blue-600\" />\n                  ) : (\n                    <BookmarkIcon className=\"w-6 h-6 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n\n              {/* Job Details */}\n              <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                <div className=\"flex items-center space-x-2 rtl:space-x-reverse text-gray-600\">\n                  <MapPinIcon className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">{job.location}, {job.country}</span>\n                </div>\n                <div className=\"flex items-center space-x-2 rtl:space-x-reverse text-gray-600\">\n                  <CurrencyDollarIcon className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">{job.salary}</span>\n                </div>\n                <div className=\"flex items-center space-x-2 rtl:space-x-reverse text-gray-600\">\n                  <ClockIcon className=\"w-4 h-4\" />\n                  <span className=\"text-sm\">{job.type}</span>\n                </div>\n                <div className=\"flex items-center space-x-2 rtl:space-x-reverse text-gray-600\">\n                  <span className=\"text-sm\">خبرة: {job.experience}</span>\n                </div>\n              </div>\n\n              {/* Job Description */}\n              <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                {job.description}\n              </p>\n\n              {/* Requirements Preview */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-semibold text-gray-900 mb-2\">المتطلبات:</h4>\n                <ul className=\"space-y-1\">\n                  {job.requirements.slice(0, 2).map((req, index) => (\n                    <li key={index} className=\"flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600\">\n                      <div className=\"w-1.5 h-1.5 bg-blue-600 rounded-full\"></div>\n                      <span>{req}</span>\n                    </li>\n                  ))}\n                  {job.requirements.length > 2 && (\n                    <li className=\"text-sm text-blue-600\">\n                      +{job.requirements.length - 2} متطلبات أخرى\n                    </li>\n                  )}\n                </ul>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-4 rtl:space-x-reverse\">\n                <Link\n                  href={`/jobs/${job.id}`}\n                  className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 text-center\"\n                >\n                  عرض التفاصيل\n                </Link>\n                <button className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-colors duration-200\">\n                  تقدم الآن\n                </button>\n              </div>\n\n              {/* Posted Date */}\n              <div className=\"mt-4 text-xs text-gray-400 text-center\">\n                نُشر منذ {new Date(job.postedDate).toLocaleDateString('ar-SA')}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* View All Jobs Button */}\n        <div className=\"text-center\">\n          <Link\n            href=\"/jobs\"\n            className=\"inline-flex items-center space-x-3 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors duration-200\"\n          >\n            <span>عرض جميع الوظائف</span>\n            <ArrowRightIcon className=\"w-5 h-5 rtl:rotate-180\" />\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturedJobsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAZA;;;;;AAgCA,MAAM,sBAAsB;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAsB;QAC1B;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,SAAS;YACT,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,UAAU;YACV,aAAa;YACb,cAAc;gBAAC;gBAA2B;gBAA0B;aAA+B;YACnG,UAAU;gBAAC;gBAAkB;gBAAW;aAAkB;YAC1D,YAAY;YACZ,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,SAAS;YACT,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,UAAU;YACV,aAAa;YACb,cAAc;gBAAC;gBAA8B;gBAA0B;aAA4B;YACnG,UAAU;gBAAC;gBAAa;gBAAa;aAAoB;YACzD,YAAY;YACZ,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,SAAS;YACT,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,UAAU;YACV,aAAa;YACb,cAAc;gBAAC;gBAA8B;gBAAuB;aAAe;YACnF,UAAU;gBAAC;gBAAgB;gBAAc;aAAU;YACnD,YAAY;YACZ,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,SAAS;YACT,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,UAAU;YACV,aAAa;YACb,cAAc;gBAAC;gBAAqB;gBAAmB;aAAyB;YAChF,UAAU;gBAAC;gBAAa;gBAAe;aAAqB;YAC5D,YAAY;YACZ,QAAQ;YACR,UAAU;QACZ;KACD;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAgB,OAAO,aAAa,MAAM;QAAC;QAC9D;YAAE,IAAI;YAAQ,MAAM;YAAmB,OAAO;QAAE;QAChD;YAAE,IAAI;YAAW,MAAM;YAAe,OAAO;QAAE;QAC/C;YAAE,IAAI;YAAS,MAAM;YAAY,OAAO;QAAE;QAC1C;YAAE,IAAI;YAAe,MAAM;YAAW,OAAO;QAAE;KAChD;IAED,MAAM,gBAAgB,CAAC;QACrB,aAAa,CAAA;YACX,MAAM,WAAW,IAAI,IAAI;YACzB,IAAI,SAAS,GAAG,CAAC,QAAQ;gBACvB,SAAS,MAAM,CAAC;YAClB,OAAO;gBACL,SAAS,GAAG,CAAC;YACf;YACA,OAAO;QACT;IACF;IAEA,MAAM,eAAe,cAAc,QAC/B,eACA,aAAa,MAAM,CAAC,CAAA;QAClB,OAAQ;YACN,KAAK;gBAAQ,OAAO,IAAI,QAAQ,KAAK;YACrC,KAAK;gBAAW,OAAO,IAAI,QAAQ,KAAK;YACxC,KAAK;gBAAS,OAAO,IAAI,QAAQ,KAAK;YACtC,KAAK;gBAAe,OAAO,IAAI,QAAQ,KAAK;YAC5C;gBAAS,OAAO;QAClB;IACF;IAEJ,qBACE,6LAAC;QAAQ,WAAU;QAAmB,KAAI;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4BAEC,SAAS,IAAM,aAAa,SAAS,EAAE;4BACvC,WAAW,CAAC,+DAA+D,EACzE,cAAc,SAAS,EAAE,GACrB,qCACA,mEACJ;;gCAED,SAAS,IAAI;8CACd,6LAAC;oCAAK,WAAW,CAAC,aAAa,EAC7B,cAAc,SAAS,EAAE,GAAG,kBAAkB,iBAC9C;;wCAAE;wCACA,SAAS,KAAK;wCAAC;;;;;;;;2BAZd,SAAS,EAAE;;;;;;;;;;8BAmBtB,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,IAAI,MAAM,kBACT,6LAAC;4DAAK,WAAU;sEAAuE;;;;;;wDAIxF,IAAI,QAAQ,kBACX,6LAAC;4DAAK,WAAU;sEAA6E;;;;;;;;;;;;8DAKjG,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sOAAA,CAAA,qBAAkB;4DAAC,WAAU;;;;;;sEAC9B,6LAAC;4DAAK,WAAU;sEAAW,IAAI,OAAO;;;;;;;;;;;;;;;;;;sDAI1C,6LAAC;4CACC,SAAS,IAAM,cAAc,IAAI,EAAE;4CACnC,WAAU;sDAET,UAAU,GAAG,CAAC,IAAI,EAAE,kBACnB,6LAAC,wNAAA,CAAA,eAAiB;gDAAC,WAAU;;;;;qEAE7B,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAM9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,sNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;;wDAAW,IAAI,QAAQ;wDAAC;wDAAG,IAAI,OAAO;;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,sOAAA,CAAA,qBAAkB;oDAAC,WAAU;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAW,IAAI,MAAM;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAK,WAAU;8DAAW,IAAI,IAAI;;;;;;;;;;;;sDAErC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAAU;oDAAO,IAAI,UAAU;;;;;;;;;;;;;;;;;;8CAKnD,6LAAC;oCAAE,WAAU;8CACV,IAAI,WAAW;;;;;;8CAIlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;gDACX,IAAI,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACtC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAM;;;;;;;uDAFA;;;;;gDAKV,IAAI,YAAY,CAAC,MAAM,GAAG,mBACzB,6LAAC;oDAAG,WAAU;;wDAAwB;wDAClC,IAAI,YAAY,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;8CAOtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;4CACvB,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAO,WAAU;sDAAgH;;;;;;;;;;;;8CAMpI,6LAAC;oCAAI,WAAU;;wCAAyC;wCAC5C,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;2BAhGnD,IAAI,EAAE;;;;;;;;;;8BAuGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;GArQM;KAAA;uCAuQS", "debugId": null}}, {"offset": {"line": 2116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Website/job-portal/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';\n\ninterface Testimonial {\n  id: string;\n  name: string;\n  position: string;\n  company: string;\n  country: string;\n  rating: number;\n  content: string;\n  avatar: string;\n  jobTitle: string;\n}\n\nconst TestimonialsSection = () => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  const testimonials: Testimonial[] = [\n    {\n      id: '1',\n      name: 'أحمد محمد العلي',\n      position: 'مهندس برمجيات',\n      company: 'شركة أرامكو السعودية',\n      country: 'السعودية',\n      rating: 5,\n      content: 'بفضل بوابة الوظائف حصلت على وظيفة أحلامي في أرامكو. الفريق كان متعاوناً جداً وساعدني في كل خطوة من إجراءات التوظيف والانتقال.',\n      avatar: '/avatars/ahmed.jpg',\n      jobTitle: 'مهندس برمجيات أول'\n    },\n    {\n      id: '2',\n      name: 'فاطمة أحمد الزهراني',\n      position: 'طبيبة أطفال',\n      company: 'مستشفى الملك فيصل التخصصي',\n      country: 'السعودية',\n      rating: 5,\n      content: 'خدمة ممتازة ومتابعة مستمرة. ساعدوني في الحصول على وظيفة في مستشفى مرموق مع راتب ممتاز ومزايا رائعة. أنصح الجميع بالتعامل معهم.',\n      avatar: '/avatars/fatima.jpg',\n      jobTitle: 'استشاري أطفال'\n    },\n    {\n      id: '3',\n      name: 'محمد عبدالله الكندي',\n      position: 'مدير مشاريع',\n      company: 'شركة نفط عمان',\n      country: 'عمان',\n      rating: 5,\n      content: 'تجربة رائعة مع بوابة الوظائف. وجدت الوظيفة المناسبة في عمان وتم تسهيل جميع إجراءات السفر والإقامة. فريق محترف ومتميز.',\n      avatar: '/avatars/mohammed.jpg',\n      jobTitle: 'مدير مشاريع أول'\n    },\n    {\n      id: '4',\n      name: 'سارة خالد المنصوري',\n      position: 'مديرة تسويق',\n      company: 'مجموعة الإمارات',\n      country: 'الإمارات',\n      rating: 5,\n      content: 'حصلت على وظيفة أحلامي في دبي بفضل الدعم المتميز من فريق بوابة الوظائف. المتابعة كانت ممتازة من البداية حتى النهاية.',\n      avatar: '/avatars/sara.jpg',\n      jobTitle: 'مديرة تسويق رقمي'\n    },\n    {\n      id: '5',\n      name: 'عبدالرحمن يوسف القطان',\n      position: 'مهندس مدني',\n      company: 'شركة قطر للبترول',\n      country: 'قطر',\n      rating: 5,\n      content: 'خدمة احترافية من الدرجة الأولى. ساعدوني في العثور على وظيفة ممتازة في قطر مع راتب مجزي ومزايا رائعة. شكراً لكم.',\n      avatar: '/avatars/abdulrahman.jpg',\n      jobTitle: 'مهندس مشاريع'\n    }\n  ];\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prevIndex) => \n      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n    );\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prevIndex) => \n      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1\n    );\n  };\n\n  const goToTestimonial = (index: number) => {\n    setCurrentIndex(index);\n  };\n\n  useEffect(() => {\n    if (isAutoPlaying) {\n      const interval = setInterval(nextTestimonial, 5000);\n      return () => clearInterval(interval);\n    }\n  }, [isAutoPlaying, currentIndex]);\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <StarIcon\n        key={index}\n        className={`w-5 h-5 ${\n          index < rating ? 'text-yellow-400' : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  return (\n    <section className=\"py-20 bg-white\" dir=\"rtl\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            ماذا يقول عملاؤنا\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            اقرأ تجارب العملاء الذين حققوا أحلامهم المهنية معنا\n          </p>\n        </div>\n\n        {/* Testimonials Carousel */}\n        <div \n          className=\"relative\"\n          onMouseEnter={() => setIsAutoPlaying(false)}\n          onMouseLeave={() => setIsAutoPlaying(true)}\n        >\n          <div className=\"overflow-hidden rounded-3xl\">\n            <div \n              className=\"flex transition-transform duration-500 ease-in-out\"\n              style={{ transform: `translateX(${currentIndex * -100}%)` }}\n            >\n              {testimonials.map((testimonial) => (\n                <div\n                  key={testimonial.id}\n                  className=\"w-full flex-shrink-0 bg-gradient-to-br from-blue-50 to-indigo-100 p-8 lg:p-12\"\n                >\n                  <div className=\"max-w-4xl mx-auto\">\n                    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 items-center\">\n                      {/* Testimonial Content */}\n                      <div className=\"lg:col-span-2 space-y-6\">\n                        <div className=\"flex items-center space-x-1 rtl:space-x-reverse\">\n                          {renderStars(testimonial.rating)}\n                        </div>\n                        \n                        <blockquote className=\"text-xl lg:text-2xl text-gray-800 leading-relaxed font-medium\">\n                          \"{testimonial.content}\"\n                        </blockquote>\n                        \n                        <div className=\"space-y-2\">\n                          <div className=\"font-bold text-gray-900 text-lg\">\n                            {testimonial.name}\n                          </div>\n                          <div className=\"text-blue-600 font-medium\">\n                            {testimonial.jobTitle}\n                          </div>\n                          <div className=\"text-gray-600\">\n                            {testimonial.company} • {testimonial.country}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Avatar and Company Info */}\n                      <div className=\"text-center lg:text-right\">\n                        <div className=\"inline-block relative\">\n                          <div className=\"w-32 h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-2xl\">\n                            {testimonial.name.split(' ')[0][0]}{testimonial.name.split(' ')[1][0]}\n                          </div>\n                          <div className=\"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center\">\n                            <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                              <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                            </svg>\n                          </div>\n                        </div>\n                        \n                        <div className=\"mt-6 p-4 bg-white rounded-xl shadow-lg\">\n                          <div className=\"text-sm text-gray-600 mb-1\">نجح في الحصول على</div>\n                          <div className=\"font-semibold text-gray-900\">{testimonial.position}</div>\n                          <div className=\"text-sm text-blue-600 mt-1\">{testimonial.company}</div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Navigation Arrows */}\n          <button\n            onClick={prevTestimonial}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 text-gray-600 p-3 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl\"\n          >\n            <ChevronLeftIcon className=\"w-6 h-6\" />\n          </button>\n          \n          <button\n            onClick={nextTestimonial}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 text-gray-600 p-3 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl\"\n          >\n            <ChevronRightIcon className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* Dots Indicator */}\n        <div className=\"flex justify-center space-x-2 rtl:space-x-reverse mt-8\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                index === currentIndex\n                  ? 'bg-blue-600 w-8'\n                  : 'bg-gray-300 hover:bg-gray-400'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Success Stats */}\n        <div className=\"mt-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-3xl p-8 lg:p-12 text-white\">\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold mb-4\">\n              قصص نجاح حقيقية\n            </h3>\n            <p className=\"text-lg text-green-100\">\n              انضم إلى آلاف المحترفين الذين حققوا أحلامهم المهنية معنا\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-yellow-300 mb-2\">98%</div>\n              <div className=\"text-green-100\">معدل رضا العملاء</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-yellow-300 mb-2\">8,000+</div>\n              <div className=\"text-green-100\">توظيف ناجح</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-yellow-300 mb-2\">30</div>\n              <div className=\"text-green-100\">يوم متوسط التوظيف</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-yellow-300 mb-2\">24/7</div>\n              <div className=\"text-green-100\">دعم مستمر</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TestimonialsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAiBA,MAAM,sBAAsB;;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,eAA8B;QAClC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,UAAU;QACZ;KACD;IAED,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;IAE5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,YACf,cAAc,IAAI,aAAa,MAAM,GAAG,IAAI,YAAY;IAE5D;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,eAAe;gBACjB,MAAM,WAAW,YAAY,iBAAiB;gBAC9C;qDAAO,IAAM,cAAc;;YAC7B;QACF;wCAAG;QAAC;QAAe;KAAa;IAEhC,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,6LAAC,gNAAA,CAAA,WAAQ;gBAEP,WAAW,CAAC,QAAQ,EAClB,QAAQ,SAAS,oBAAoB,iBACrC;eAHG;;;;;IAMX;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAiB,KAAI;kBACtC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBACC,WAAU;oBACV,cAAc,IAAM,iBAAiB;oBACrC,cAAc,IAAM,iBAAiB;;sCAErC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,WAAW,CAAC,WAAW,EAAE,eAAe,CAAC,IAAI,EAAE,CAAC;gCAAC;0CAEzD,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,YAAY,YAAY,MAAM;;;;;;0EAGjC,6LAAC;gEAAW,WAAU;;oEAAgE;oEAClF,YAAY,OAAO;oEAAC;;;;;;;0EAGxB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,YAAY,IAAI;;;;;;kFAEnB,6LAAC;wEAAI,WAAU;kFACZ,YAAY,QAAQ;;;;;;kFAEvB,6LAAC;wEAAI,WAAU;;4EACZ,YAAY,OAAO;4EAAC;4EAAI,YAAY,OAAO;;;;;;;;;;;;;;;;;;;kEAMlD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;4EAAE,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;;;;;;;kFAEvE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAAqB,MAAK;4EAAe,SAAQ;sFAC9D,cAAA,6LAAC;gFAAK,UAAS;gFAAU,GAAE;gFAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;0EAK/J,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAA6B;;;;;;kFAC5C,6LAAC;wEAAI,WAAU;kFAA+B,YAAY,QAAQ;;;;;;kFAClE,6LAAC;wEAAI,WAAU;kFAA8B,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA5CnE,YAAY,EAAE;;;;;;;;;;;;;;;sCAuD3B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,8NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAG7B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,gOAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKhC,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,oBACA,iCACJ;2BANG;;;;;;;;;;8BAYX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;sCAKxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,6LAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,6LAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,6LAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,6LAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;GAhPM;KAAA;uCAkPS", "debugId": null}}, {"offset": {"line": 2672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Website/job-portal/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { \n  CalendarDaysIcon,\n  ClockIcon,\n  ArrowRightIcon,\n  TagIcon,\n  UserIcon\n} from '@heroicons/react/24/outline';\n\ninterface NewsArticle {\n  id: string;\n  title: string;\n  excerpt: string;\n  content: string;\n  author: string;\n  publishDate: string;\n  readTime: string;\n  category: string;\n  tags: string[];\n  image: string;\n  featured: boolean;\n}\n\nconst NewsSection = () => {\n  const newsArticles: NewsArticle[] = [\n    {\n      id: '1',\n      title: 'أحدث اتجاهات سوق العمل في دول الخليج لعام 2024',\n      excerpt: 'تعرف على أهم التطورات والفرص الجديدة في سوق العمل الخليجي وأكثر التخصصات المطلوبة',\n      content: 'يشهد سوق العمل في دول الخليج تطورات مهمة...',\n      author: 'د. أحمد الخبير',\n      publishDate: '2024-01-15',\n      readTime: '5 دقائق',\n      category: 'سوق العمل',\n      tags: ['الخليج', 'وظائف', 'اتجاهات'],\n      image: '/news/gulf-market-2024.jpg',\n      featured: true\n    },\n    {\n      id: '2',\n      title: 'نصائح لكتابة سيرة ذاتية مميزة تجذب أصحاب العمل',\n      excerpt: 'دليل شامل لإنشاء سيرة ذاتية احترافية تزيد من فرصك في الحصول على الوظيفة المناسبة',\n      content: 'السيرة الذاتية هي بطاقة التعريف الأولى...',\n      author: 'سارة المستشارة',\n      publishDate: '2024-01-12',\n      readTime: '7 دقائق',\n      category: 'نصائح مهنية',\n      tags: ['سيرة ذاتية', 'نصائح', 'توظيف'],\n      image: '/news/cv-tips.jpg',\n      featured: false\n    },\n    {\n      id: '3',\n      title: 'كيفية التحضير لمقابلة العمل عبر الإنترنت',\n      excerpt: 'مع انتشار المقابلات الافتراضية، إليك أهم النصائح للنجاح في مقابلة العمل عن بُعد',\n      content: 'أصبحت المقابلات عبر الإنترنت جزءاً أساسياً...',\n      author: 'محمد الاستشاري',\n      publishDate: '2024-01-10',\n      readTime: '6 دقائق',\n      category: 'مقابلات العمل',\n      tags: ['مقابلات', 'عن بُعد', 'نصائح'],\n      image: '/news/online-interview.jpg',\n      featured: false\n    },\n    {\n      id: '4',\n      title: 'أفضل الشهادات المهنية المطلوبة في 2024',\n      excerpt: 'قائمة بأهم الشهادات والدورات التدريبية التي تزيد من قيمتك في سوق العمل',\n      content: 'في عالم متسارع التطور، تلعب الشهادات المهنية...',\n      author: 'فاطمة الخبيرة',\n      publishDate: '2024-01-08',\n      readTime: '8 دقائق',\n      category: 'التطوير المهني',\n      tags: ['شهادات', 'تدريب', 'مهارات'],\n      image: '/news/certifications-2024.jpg',\n      featured: true\n    }\n  ];\n\n  const categories = [\n    { name: 'سوق العمل', count: 12, color: 'bg-blue-100 text-blue-600' },\n    { name: 'نصائح مهنية', count: 8, color: 'bg-green-100 text-green-600' },\n    { name: 'مقابلات العمل', count: 6, color: 'bg-purple-100 text-purple-600' },\n    { name: 'التطوير المهني', count: 10, color: 'bg-orange-100 text-orange-600' }\n  ];\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('ar-SA', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\" dir=\"rtl\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n            أحدث الأخبار والمقالات\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            ابق على اطلاع بأحدث التطورات في عالم التوظيف والمهن\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-3\">\n            {/* Featured Article */}\n            <div className=\"mb-12\">\n              <div className=\"bg-white rounded-3xl overflow-hidden shadow-xl\">\n                <div className=\"relative h-64 lg:h-80 bg-gradient-to-r from-blue-600 to-purple-600\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-40\"></div>\n                  <div className=\"absolute bottom-0 left-0 right-0 p-8 text-white\">\n                    <div className=\"flex items-center space-x-4 rtl:space-x-reverse mb-4\">\n                      <span className=\"bg-yellow-500 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold\">\n                        مقال مميز\n                      </span>\n                      <span className=\"bg-white bg-opacity-20 backdrop-blur-sm px-3 py-1 rounded-full text-sm\">\n                        {newsArticles[0].category}\n                      </span>\n                    </div>\n                    <h3 className=\"text-2xl lg:text-3xl font-bold mb-3 leading-tight\">\n                      {newsArticles[0].title}\n                    </h3>\n                    <p className=\"text-lg text-gray-200 mb-4 leading-relaxed\">\n                      {newsArticles[0].excerpt}\n                    </p>\n                    <div className=\"flex items-center space-x-6 rtl:space-x-reverse text-sm\">\n                      <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                        <UserIcon className=\"w-4 h-4\" />\n                        <span>{newsArticles[0].author}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                        <CalendarDaysIcon className=\"w-4 h-4\" />\n                        <span>{formatDate(newsArticles[0].publishDate)}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                        <ClockIcon className=\"w-4 h-4\" />\n                        <span>{newsArticles[0].readTime}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"p-8\">\n                  <Link\n                    href={`/blog/${newsArticles[0].id}`}\n                    className=\"inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors duration-200\"\n                  >\n                    <span>اقرأ المقال كاملاً</span>\n                    <ArrowRightIcon className=\"w-4 h-4 rtl:rotate-180\" />\n                  </Link>\n                </div>\n              </div>\n            </div>\n\n            {/* Other Articles */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {newsArticles.slice(1).map((article) => (\n                <article\n                  key={article.id}\n                  className=\"bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\"\n                >\n                  <div className=\"h-48 bg-gradient-to-br from-gray-200 to-gray-300 relative\">\n                    <div className=\"absolute top-4 right-4\">\n                      <span className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                        article.category === 'نصائح مهنية' ? 'bg-green-100 text-green-600' :\n                        article.category === 'مقابلات العمل' ? 'bg-purple-100 text-purple-600' :\n                        'bg-orange-100 text-orange-600'\n                      }`}>\n                        {article.category}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"p-6\">\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-3 leading-tight\">\n                      {article.title}\n                    </h3>\n                    \n                    <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                      {article.excerpt}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                        <UserIcon className=\"w-4 h-4\" />\n                        <span>{article.author}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                        <ClockIcon className=\"w-4 h-4\" />\n                        <span>{article.readTime}</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-500\">\n                        <CalendarDaysIcon className=\"w-4 h-4\" />\n                        <span>{formatDate(article.publishDate)}</span>\n                      </div>\n                      \n                      <Link\n                        href={`/blog/${article.id}`}\n                        className=\"text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center space-x-1 rtl:space-x-reverse\"\n                      >\n                        <span>اقرأ المزيد</span>\n                        <ArrowRightIcon className=\"w-4 h-4 rtl:rotate-180\" />\n                      </Link>\n                    </div>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-8\">\n            {/* Categories */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                التصنيفات\n              </h3>\n              <div className=\"space-y-3\">\n                {categories.map((category, index) => (\n                  <Link\n                    key={index}\n                    href={`/blog/category/${category.name}`}\n                    className=\"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <span className=\"font-medium text-gray-700\">\n                      {category.name}\n                    </span>\n                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${category.color}`}>\n                      {category.count}\n                    </span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* Newsletter Signup */}\n            <div className=\"bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-6 text-white\">\n              <h3 className=\"text-xl font-bold mb-4\">\n                اشترك في النشرة الإخبارية\n              </h3>\n              <p className=\"text-blue-100 mb-6\">\n                احصل على أحدث المقالات والنصائح المهنية مباشرة في بريدك الإلكتروني\n              </p>\n              <div className=\"space-y-3\">\n                <input\n                  type=\"email\"\n                  placeholder=\"بريدك الإلكتروني\"\n                  className=\"w-full px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-400\"\n                />\n                <button className=\"w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors duration-200\">\n                  اشترك الآن\n                </button>\n              </div>\n            </div>\n\n            {/* Popular Tags */}\n            <div className=\"bg-white rounded-2xl p-6 shadow-lg\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                الكلمات المفتاحية الشائعة\n              </h3>\n              <div className=\"flex flex-wrap gap-2\">\n                {['وظائف', 'الخليج', 'سيرة ذاتية', 'مقابلات', 'تطوير مهني', 'نصائح', 'شهادات', 'تدريب'].map((tag, index) => (\n                  <Link\n                    key={index}\n                    href={`/blog/tag/${tag}`}\n                    className=\"inline-flex items-center space-x-1 rtl:space-x-reverse bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-full text-sm transition-colors duration-200\"\n                  >\n                    <TagIcon className=\"w-3 h-3\" />\n                    <span>{tag}</span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/blog\"\n            className=\"inline-flex items-center space-x-3 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors duration-200\"\n          >\n            <span>عرض جميع المقالات</span>\n            <ArrowRightIcon className=\"w-5 h-5 rtl:rotate-180\" />\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default NewsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAyBA,MAAM,cAAc;IAClB,MAAM,eAA8B;QAClC;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,SAAS;YACT,QAAQ;YACR,aAAa;YACb,UAAU;YACV,UAAU;YACV,MAAM;gBAAC;gBAAU;gBAAS;aAAU;YACpC,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,SAAS;YACT,QAAQ;YACR,aAAa;YACb,UAAU;YACV,UAAU;YACV,MAAM;gBAAC;gBAAc;gBAAS;aAAQ;YACtC,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,SAAS;YACT,QAAQ;YACR,aAAa;YACb,UAAU;YACV,UAAU;YACV,MAAM;gBAAC;gBAAW;gBAAW;aAAQ;YACrC,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,SAAS;YACT,QAAQ;YACR,aAAa;YACb,UAAU;YACV,UAAU;YACV,MAAM;gBAAC;gBAAU;gBAAS;aAAS;YACnC,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,OAAO;YAAI,OAAO;QAA4B;QACnE;YAAE,MAAM;YAAe,OAAO;YAAG,OAAO;QAA8B;QACtE;YAAE,MAAM;YAAiB,OAAO;YAAG,OAAO;QAAgC;QAC1E;YAAE,MAAM;YAAkB,OAAO;YAAI,OAAO;QAAgC;KAC7E;IAED,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAmB,KAAI;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2E;;;;;;kFAG3F,6LAAC;wEAAK,WAAU;kFACb,YAAY,CAAC,EAAE,CAAC,QAAQ;;;;;;;;;;;;0EAG7B,6LAAC;gEAAG,WAAU;0EACX,YAAY,CAAC,EAAE,CAAC,KAAK;;;;;;0EAExB,6LAAC;gEAAE,WAAU;0EACV,YAAY,CAAC,EAAE,CAAC,OAAO;;;;;;0EAE1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,kNAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;0FAAM,YAAY,CAAC,EAAE,CAAC,MAAM;;;;;;;;;;;;kFAE/B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,kOAAA,CAAA,mBAAgB;gFAAC,WAAU;;;;;;0FAC5B,6LAAC;0FAAM,WAAW,YAAY,CAAC,EAAE,CAAC,WAAW;;;;;;;;;;;;kFAE/C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;0FACrB,6LAAC;0FAAM,YAAY,CAAC,EAAE,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE;oDACnC,WAAU;;sEAEV,6LAAC;sEAAK;;;;;;sEACN,6LAAC,8NAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOlC,6LAAC;oCAAI,WAAU;8CACZ,aAAa,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,wBAC1B,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAW,CAAC,6CAA6C,EAC7D,QAAQ,QAAQ,KAAK,gBAAgB,gCACrC,QAAQ,QAAQ,KAAK,kBAAkB,kCACvC,iCACA;sEACC,QAAQ,QAAQ;;;;;;;;;;;;;;;;8DAKvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAGhB,6LAAC;4DAAE,WAAU;sEACV,QAAQ,OAAO;;;;;;sEAGlB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,kNAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;sFAAM,QAAQ,MAAM;;;;;;;;;;;;8EAEvB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;sFACrB,6LAAC;sFAAM,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;sEAI3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,kOAAA,CAAA,mBAAgB;4EAAC,WAAU;;;;;;sFAC5B,6LAAC;sFAAM,WAAW,QAAQ,WAAW;;;;;;;;;;;;8EAGvC,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;oEAC3B,WAAU;;sFAEV,6LAAC;sFAAK;;;;;;sFACN,6LAAC,8NAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CA9C3B,QAAQ,EAAE;;;;;;;;;;;;;;;;sCAwDvB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,CAAC,eAAe,EAAE,SAAS,IAAI,EAAE;oDACvC,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEACb,SAAS,IAAI;;;;;;sEAEhB,6LAAC;4DAAK,WAAW,CAAC,6CAA6C,EAAE,SAAS,KAAK,EAAE;sEAC9E,SAAS,KAAK;;;;;;;mDARZ;;;;;;;;;;;;;;;;8CAgBb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyB;;;;;;sDAGvC,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAO,WAAU;8DAA2H;;;;;;;;;;;;;;;;;;8CAOjJ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAS;gDAAU;gDAAc;gDAAW;gDAAc;gDAAS;gDAAU;6CAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,sBAChG,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,CAAC,UAAU,EAAE,KAAK;oDACxB,WAAU;;sEAEV,6LAAC,gNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;sEAAM;;;;;;;mDALF;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAcjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;KAlRM;uCAoRS", "debugId": null}}]}