'use client';

import Link from 'next/link';
import { 
  GlobeAltIcon,
  DocumentCheckIcon,
  UserGroupIcon,
  BuildingOfficeIcon,
  AcademicCapIcon,
  HeartIcon,
  CogIcon,
  TruckIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface Service {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  features: string[];
  link: string;
}

const ServicesSection = () => {
  const services: Service[] = [
    {
      id: 'international',
      title: 'التوظيف الدولي',
      description: 'نربطك بأفضل الفرص الوظيفية في دول الخليج وحول العالم',
      icon: GlobeAltIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      features: [
        'وظائف في دول الخليج',
        'فرص في أوروبا وأمريكا',
        'وظائف في آسيا وأفريقيا',
        'رواتب تنافسية'
      ],
      link: '/services/international'
    },
    {
      id: 'visa',
      title: 'خدمات التأشيرة',
      description: 'نساعدك في جميع إجراءات التأشيرة والسفر بسهولة ويسر',
      icon: DocumentCheckIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      features: [
        'استخراج تأشيرة العمل',
        'ترجمة الوثائق',
        'الفحص الطبي',
        'متابعة الإجراءات'
      ],
      link: '/services/visa'
    },
    {
      id: 'recruitment',
      title: 'استشارات التوظيف',
      description: 'خدمات استشارية متخصصة للشركات في مجال التوظيف',
      icon: UserGroupIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      features: [
        'تقييم المرشحين',
        'اختبارات المهارات',
        'المقابلات المتخصصة',
        'التوظيف السريع'
      ],
      link: '/services/recruitment'
    },
    {
      id: 'corporate',
      title: 'الحلول المؤسسية',
      description: 'حلول توظيف شاملة للشركات الكبيرة والمؤسسات',
      icon: BuildingOfficeIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      features: [
        'توظيف جماعي',
        'إدارة المواهب',
        'التدريب والتطوير',
        'الاستشارات الإدارية'
      ],
      link: '/services/corporate'
    }
  ];

  const specializations = [
    {
      icon: CogIcon,
      title: 'الهندسة والتقنية',
      count: '850+ وظيفة',
      color: 'text-blue-600'
    },
    {
      icon: HeartIcon,
      title: 'الطب والصحة',
      count: '420+ وظيفة',
      color: 'text-red-600'
    },
    {
      icon: AcademicCapIcon,
      title: 'التعليم والتدريب',
      count: '320+ وظيفة',
      color: 'text-green-600'
    },
    {
      icon: TruckIcon,
      title: 'اللوجستيات والنقل',
      count: '280+ وظيفة',
      color: 'text-yellow-600'
    }
  ];

  return (
    <section className="py-20 bg-white" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            خدماتنا المتميزة
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            نقدم مجموعة شاملة من الخدمات لضمان حصولك على أفضل الفرص الوظيفية
          </p>
        </div>

        {/* Main Services */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {services.map((service) => {
            const IconComponent = service.icon;
            
            return (
              <div
                key={service.id}
                className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                <div className={`w-16 h-16 ${service.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className={`w-8 h-8 ${service.color}`} />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
                      <div className={`w-2 h-2 rounded-full ${service.color.replace('text-', 'bg-')}`}></div>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Link
                  href={service.link}
                  className={`inline-flex items-center space-x-2 rtl:space-x-reverse ${service.color} hover:underline font-medium group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-200`}
                >
                  <span>اعرف المزيد</span>
                  <ArrowRightIcon className="w-4 h-4 rtl:rotate-180" />
                </Link>
              </div>
            );
          })}
        </div>

        {/* Specializations */}
        <div className="bg-gray-50 rounded-3xl p-8 lg:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              التخصصات الأكثر طلباً
            </h3>
            <p className="text-lg text-gray-600">
              اكتشف الفرص المتاحة في أهم التخصصات المطلوبة في السوق
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {specializations.map((spec, index) => {
              const IconComponent = spec.icon;
              
              return (
                <div
                  key={index}
                  className="bg-white rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                >
                  <div className="flex justify-center mb-4">
                    <div className={`w-12 h-12 bg-gray-50 rounded-xl flex items-center justify-center`}>
                      <IconComponent className={`w-6 h-6 ${spec.color}`} />
                    </div>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {spec.title}
                  </h4>
                  <p className={`text-sm font-medium ${spec.color}`}>
                    {spec.count}
                  </p>
                </div>
              );
            })}
          </div>

          <div className="text-center">
            <Link
              href="/jobs"
              className="inline-flex items-center space-x-3 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors duration-200"
            >
              <span>تصفح جميع الوظائف</span>
              <ArrowRightIcon className="w-5 h-5 rtl:rotate-180" />
            </Link>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 lg:p-12 text-white text-center">
          <h3 className="text-2xl lg:text-3xl font-bold mb-4">
            هل تحتاج مساعدة في العثور على الوظيفة المناسبة؟
          </h3>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            فريقنا من الخبراء جاهز لمساعدتك في كل خطوة من رحلة البحث عن العمل
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-xl transition-colors duration-200"
            >
              تواصل معنا
            </Link>
            <Link
              href="/candidates/register"
              className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 rounded-xl transition-all duration-200"
            >
              سجل الآن مجاناً
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
